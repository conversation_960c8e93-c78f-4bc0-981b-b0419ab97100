# 🧠 JavaScript Code Guidelines for Bright Sparks

This guide outlines our best practices for writing readable, safe, and maintainable JavaScript code.

---

## 1. Use JSDoc for Type Annotations

Use JSDoc to describe expected input and return types.

[Here's a good article going over the basics of JSDoc!](https://blog.shhdharmen.me/how-to-utilise-jsdoc-comment-tags-so-that-visual-studio-code-intellisense-works-great)

```js
/**
 * @param {string} name
 * @param {number} age
 * @returns {string}
 */
function greet(name, age) {
  return `Hi ${name}, you are ${age} years old.`;
}
```

✅ Helps catch type errors early and improves editor support.

---

## 2. Check All Function Inputs Explicitly

Always validate function inputs.

```js
function addToCart(productId, quantity) {
  if (typeof productId !== 'string') throw new Error('productId must be a string');
  if (typeof quantity !== 'number') throw new Error('quantity must be a number');
}
```

✅ Prevents bugs from incorrect usage.

---

## 3. Use `Array.isArray()` and `typeof` Defensively

Avoid common pitfalls with JavaScript type checking.

```js
if (typeof user === 'object' && user !== null) {
  // Safe to access user.name
}
```

✅ Correctly identifies arrays, objects, and nulls.

---

## 4. Define Clear Object Shapes

Use typedefs or schemas to describe complex objects.

```js
/**
 * @typedef {Object} Session
 * @property {string} id
 * @property {string} studentId
 * @property {Date} scheduledTime
 */

/** @type {Session} */
const exampleSession = {
  id: 'abc123',
  studentId: 'stu456',
  scheduledTime: new Date(),
};
```

✅ Makes data structures self-documenting.

---

## 5. Avoid Implicit Type Coercion

Be explicit with types and comparisons.

```js
if (value === 0) // Strict equality
```

```js
Number(input), String(value), Boolean(flag)
```

✅ Avoids bugs from unexpected coercion.

---

## 6. Validate External Data (e.g., Firebase)

Check shape and types before using external data.

```js
const doc = await getDoc(ref);
const data = doc.data();

if (data && typeof data.name === 'string') {
  // Safe to use data.name
}
```

✅ Prevents runtime errors and crashes.

---

## 7. Prefer Constants and Enums Over Magic Strings

Use constants or frozen objects.

```js
const USER_ROLES = Object.freeze({
  STUDENT: 'student',
  TUTOR: 'tutor',
  ADMIN: 'admin',
});
```

✅ Prevents typos and centralizes values.

---

## 8. Use ESLint + Prettier with Type-Safe Rules

Configure tools with rules like:

- `no-implicit-coercion`
- `valid-jsdoc`
- `no-unused-vars`
- `eqeqeq`

✅ Ensures consistent, error-resistant code.

---

## 9. Comment Edge Cases and Assumptions

Document anything that’s not obvious.

```js
/**
 * Returns display name or 'Anonymous' if user.name is null.
 * @param {{ name?: string | null }} user
 * @returns {string}
 */
function getDisplayName(user) {
  return user?.name || 'Anonymous';
}
```

✅ Makes expectations clear to collaborators.

---

_Please stick to these practices!! We’ll avoid a lot of bugs before they happen if we do!_