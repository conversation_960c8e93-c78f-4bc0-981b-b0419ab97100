'use client';
import {useState} from "react";
//import { auth } from "@clerk/nextjs/server";

//components
import sidebar from "@/app/components/parents/sidebar.jsx";
import sessions from "@/app/components/parents/sessions.jsx";
import grades from "@/app/components/parents/grades.jsx";

function StudentProgress() {

    const [activeTab, setActiveTab] = useState("tab1");
    const tabs = [
            { id:"tab1", label:"Overview"},
            { id:"tab2", label:"Monthly Report"},
            { id:"tab3", label:"Upcoming Sessions"},
            { id:"tab4", label:"Milestones"},
        ];
    
    //let notif=[0,0,0,0];//notif is array with notif per tab, correspond to id order. placeholder.

    const tabContent = {
        tab1: (
            <>
                <div>{sessions()}</div>
                <br/>
                <div>{grades()}</div>

            </>
        ),
        tab2: (
            <p>placeholder</p>
        ),
        tab3: (
            <>
                <p>placeholder</p>
                <div>{sessions()}</div>
            </>
            
        ),
        tab4: (
            <p>placeholder</p>
        ),
    };

    return (
        
        <>
        <div className="grid grid-cols-6">
            <div className="bg-yellow-500">
                {sidebar()}
            </div>
            <div className="col-span-5 h-screen text-white bg-brand-yellow">
                <h1 className="text-2xl font-bold text-black p-4 mx-4">Student Progress</h1>
                <div className="min-h-screen w-full flex-grow items-center">
                    <div className="rounded-3xl p-8 mx-10 space-y-5">
                        <div className="flex border-b justify-left">
                            {tabs.map((tab) => (
                                <button
                                    key={tab.id}
                                    className={`px-4 py-2 ${
                                        activeTab === tab.id ? "border-b-2 border-black text-black" : "text-gray-500"
                                    }`}
                                    onClick={() => setActiveTab(tab.id)}
                                >
                                    {tab.label || tab.id}
                                </button>
                            ))}
                        </div>

                        <div className="text-black">{tabContent[activeTab]}</div>

                    </div>

                </div>
            </div>
        </div>
        </>
  );
}

export default StudentProgress;