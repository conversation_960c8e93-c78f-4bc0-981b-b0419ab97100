import { app, db } from '../config/firebase';
import { collection } from 'firebase/firestore';

describe('Firebase Connection', () => {
  test('Firebase app should be initialized', () => {
    expect(app).toBeDefined();
    expect(app.name).toBe('[DEFAULT]');
  });

  test('Firestore should be initialized', () => {
    expect(db).toBeDefined();
    // Test that we can create a collection reference
    const testCollection = collection(db, 'test_connection');
    expect(testCollection).toBeDefined();
  });
});
