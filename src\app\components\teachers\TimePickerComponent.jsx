"use client";

import dynamic from "next/dynamic";
import "react-time-picker/dist/TimePicker.css";
import "react-clock/dist/Clock.css";

// Dynamically import react-time-picker for Next.js SSR compatibility
const TimePicker = dynamic(() => import("react-time-picker"), { ssr: false });

export default function TimePickerComponent({ value, onChange }) {
  return (
    <TimePicker
      onChange={onChange}
      value={value}
      disableClock
      clearIcon={null}
      className="w-full
        border
        border-gray-300
        rounded-md
        px-3
        py-2
        text-gray-900
        placeholder-gray-400
        focus:outline-none
        focus:ring-2
        focus:ring-indigo-500
        focus:border-indigo-500
        transition
        shadow-sm
        cursor-pointer
        "
    />
  );
}
