import { useRouter } from 'next/navigation';

export function useAppRouter() {
  const router = useRouter();

  return {
    navigateToHome: () => router.push('/home'),
    navigateToRoot: () => router.push('/'),

    navigateToParents: () => router.push('/parents'),
    navigateToParentsStudentProgress: () => router.push('/parents/student-progress'),
    navigateToParentsDashboard: () => router.push('/parents/dashboard'),
  };
}

