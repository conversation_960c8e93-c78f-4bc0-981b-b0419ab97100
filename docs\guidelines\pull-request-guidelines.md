# 🔀 Pull Request Guidelines

To keep our codebase clean, maintainable, and easy to review, **follow these rules when opening or reviewing a pull request**.

This will be mostly the same as the commit guidelines...

---

## 🧩 1. Title

Use a **clear and concise title** that matches the main purpose of the PR. Preferably use the same format as commit titles:

```
[type(scope)]: brief, imperative title
```

### ✅ Examples:
```
feat(dashboard): add tutor feedback section
fix(auth): resolve session expiration issue
docs: update README with Firebase setup
```

---

## 📋 2. Description

Include a description that answers:
- **What** was done?
- **Why** was it done?
- **How** was it done (optional)?
- Any **special notes**, blockers, or next steps?

### ✅ Example:
```
## Summary

Adds a feedback section to the student dashboard that shows comments from past sessions.

## Changes
- Created FeedbackSection component
- Integrated Firestore query to fetch session feedback
- Styled layout with Tailwind

## Notes
- Need final styles from the design team
- Next step: enable inline response from tutors
```

---

## ✅ 3. Checklist Before Submitting

- [ ] PR title follows conventions
- [ ] All tests pass locally
- [ ] Code is linted and formatted
- [ ] No unrelated changes included
- [ ] Linked to a GitHub issue (if applicable)
- [ ] Includes screenshots or video if UI was updated

---

## 📎 4. Linking Issues

If the PR resolves a GitHub issue, reference it in the description so it auto-closes when merged:

```
Closes #12
Fixes #34
```

---

## 👀 5. Reviewing a PR

When reviewing someone else’s pull request:

- Be constructive, not just critical
- Use inline comments for specific suggestions
- Approve only if the PR meets all requirements
- If changes are requested, be clear and respectful

---

## 🚫 What to Avoid

- ❌ Titles like `misc changes`, `quick fix`, or `final version`
- ❌ Pushing directly to `main` (always go through a PR)
- ❌ Submitting code without testing
