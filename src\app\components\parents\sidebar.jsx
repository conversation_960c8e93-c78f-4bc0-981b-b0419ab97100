function Sidebar() {
    return(
        <div className="w-xs h-screen bg-yellow-500 text-black justify-left">
            <div className="min-h-screen w-full flex-grow items-left bg-yellow-50">
                <div className="rounded-3xl p-3 ml-3 space-y-5">
                    <h1 className="font-bold text-xl text-black mb-4">Logo</h1>
                    <p className="text-xs text-nowrap">change numbers to image and add links</p>
                    <div className="grid grid-cols-3">
                        <a href="#" className="">1</a>
                        <a href="#" className="">2</a>
                        <a href="#" className="">3</a>
                    </div>

                    <p className="text-xs">search bar</p>
                    <ul className="space-y-2">
                        <li><a href="./dashboard" className="text-black hover:text-grey-50">Dashboard</a></li>
                        <li><a href="./student-progress" className="text-black hover:text-grey-50">Student Progress</a></li>
                        <li><a href="#" className="text-black hover:text-grey-50">Contact</a></li>
                    </ul>
                </div>
            </div>
        </div>
    );
    //Notes:
    //add link to contact page when contact page is created.
    //add logo when given access.
    //I am not sure what the search bar is supposed to do, so I just added a placeholder text.
    //change number links to images and add links when pages made and images given.
}

//potentially add change to make sidebar dropdown on smaller screens
export default Sidebar;