# 📝 Git Commit Guidelines

To keep our commit history clean, searchable, and easy to understand, **please follow these conventions** for all commit titles and descriptions.

---

## 🔠 Commit Title (Required)

- **Use the imperative mood** (e.g., "Add", "Fix", "Refactor", not "Added", "Fixes", "Refactored")
- **Keep it under 50 characters**
- **Start with a lowercase type and scope in brackets** followed by a concise summary

### ✅ Format
```
[type(scope)]: brief, imperative title
```

### ✅ Examples
```
[feat(auth)]: add student sign-up with <PERSON>
[fix(homework)]: correct typo in assignment prompt
[chore(firebase)]: update rules for public read access
[refactor(ui)]: simplify Dashboard layout logic
[docs(readme)]: add setup instructions for local dev
```

---

## 🎯 Common Types (Prefixes)

| Type       | Purpose                                     |
|------------|---------------------------------------------|
| `feat`     | New feature or functionality                |
| `fix`      | Bug fixes                                   |
| `refactor` | Code restructuring (no functional change)   |
| `style`    | Formatting only (no code changes)           |
| `docs`     | Documentation changes                       |
| `test`     | Adding or updating tests                    |
| `chore`    | Maintenance (e.g., deps, build, config)     |

---

## 📄 Commit Description (Optional but Encouraged)

- Use if the title needs clarification or if multiple related changes are included.
- Leave **one blank line** after the title.
- Focus on **why** the change was made, not just what changed.
- Bullet points are helpful for listing detailed changes.

### ✅ Example Description
```
feat(schedule): display upcoming sessions on dashboard

- Add "Upcoming Sessions" section under student dashboard
- Pull data from Firestore 'sessions' collection
- Include Zoom link and date filtering
```

---

## ⛔️ What to Avoid

- ❌ Vague titles like `update`, `fix stuff`, `changes`
- ❌ Using past tense (`Added`, `Fixed`)
- ❌ Bundling unrelated changes into one commit

---

Following this structure makes collaboration easier and helps us understand project history at a glance. Thanks!
