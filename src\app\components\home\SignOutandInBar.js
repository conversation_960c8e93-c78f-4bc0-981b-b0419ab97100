import Link from 'next/link';
import { SignIn<PERSON><PERSON><PERSON>, Sign<PERSON><PERSON><PERSON>utt<PERSON>, User<PERSON>utton, SignedIn, SignedOut } from '@clerk/nextjs';

export default function SignOutandInBar() {
  return (
    <nav className="bg-white shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <SignedIn>
              <UserButton afterSignOutUrl="/home" />
            </SignedIn>
            <SignedOut>
              <SignInButton mode="modal">
                <button className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900">
                  Sign in
                </button>
              </SignInButton>
              <SignUpButton mode="modal">
                <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                  Sign up
                </button>
              </SignUpButton>
            </SignedOut>
          </div>
        </div>
      </div>
    </nav>
  );
}
