import { auth } from "@clerk/nextjs/server";
import SignOutandInBar from '@/app/components/home/<USER>';

export default async function Home() {
  const { userId } = await auth();

  return (
    <div className="min-h-screen bg-gray-50">
      <SignOutandInBar />
      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-8">
            Welcome to SparkSmart
          </h1>
          <p className="text-lg mb-8">
            {userId 
              ? "You're signed in! Start exploring our features."
              : "Sign in to get started with our platform."}
          </p>
        </div>
      </main>
    </div>
  );
}
