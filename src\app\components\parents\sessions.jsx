import React, { useState } from 'react';

const sessions = [
  { teacher: 'Teacher 1', subject: 'Math', classNum: 1, date: '6/14', time: '3pm - 4pm', link: '#' },
  { teacher: 'Teacher 2', subject: 'Computer Science', classNum: 1, date: '6/15', time: '4pm - 5pm', link: '#' },
  { teacher: 'Teacher 1', subject: 'Math', classNum: 2, date: '6/16', time: '3pm - 4pm', link: '#' },
  { teacher: 'Teacher 2', subject: 'Computer Science', classNum: 2, date: '6/17', time: '4pm - 5pm', link: '#' },
];
//I am not sure what the 3 dots on the side of the sessions tab are supposed to do.
const SessionTable = () => {
  const [currentPage, setCurrentPage] = useState(1);

  return (
    <div className="p-6 max-w-6xl mx-auto bg-white rounded-lg shadow-md">
      <table className="w-full text-left border-collapse">
        <thead>
          <tr className="bg-gray-100 text-gray-700">
            <th className="py-3 px-4">Upcoming Sessions</th>
            <th className="py-3 px-4">Class Name</th>
            <th className="py-3 px-4">Class Number</th>
            <th className="py-3 px-4">Date</th>
            <th className="py-3 px-4">Time (PDT)</th>
            <th className="py-3 px-4">Meeting Links</th>
          </tr>
        </thead>
        <tbody>
          {sessions.map((session, i) => (
            <tr key={i} className="border-t rounded-lg bg-white shadow-md hover:bg-gray-100">
              <td className="py-3 px-4">{session.teacher}</td>
              <td className="py-3 px-4">{session.subject}</td>
              <td className="py-3 px-4">{session.classNum}</td>
              <td className="py-3 px-4">{session.date}</td>
              <td className="py-3 px-4">{session.time}</td>
              <td className="py-3 px-4">
                <a href={session.link} className="bg-gray-200 text-sm px-3 py-1 rounded-full hover:bg-gray-300">Zoom</a>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Pages - hard set the page count to 5 right now*/}
      <div className="flex justify-center items-center mt-6 space-x-2 bg">
        <button
          className="text-sm px-3 py-1 rounded hover:bg-gray-100"
          disabled={currentPage === 1}
          onClick={() => setCurrentPage(currentPage - 1)}
        >
          Previous
        </button>

        {[1, 2, 3, 4, 5].map((num) => (
          <button
            key={num}
            onClick={() => setCurrentPage(num)}
            className={`px-3 py-1 rounded text-sm ${
              currentPage === num ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
            }`}
          >
            {num}
          </button>
        ))}

        <button
          className="text-sm px-3 py-1 rounded hover:bg-gray-100"
          onClick={() => setCurrentPage(currentPage + 1)}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default SessionTable;