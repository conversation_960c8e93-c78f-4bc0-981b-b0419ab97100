{"name": "sparksmart", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest"}, "dependencies": {"@clerk/nextjs": "^6.21.0", "firebase": "^11.9.0", "firebase-admin": "^13.4.0", "next": "15.3.3", "react": "^19.0.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-time-picker": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "babel-jest": "^30.0.0-beta.3", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}