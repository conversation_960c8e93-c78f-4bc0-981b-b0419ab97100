name: 
run-name: Push to ${{ github.ref_name }} by @${{ github.actor }}
on: [push]
jobs:
  build:
    environment: sparksmart-env
    env: 
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:        ${{ secrets.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY }}
      CLERK_SECRET_KEY:                         ${{ secrets.CLERK_SECRET_KEY }}
      NEXT_PUBLIC_FIREBASE_API_KEY:             ${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}
      NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN:         ${{ secrets.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
      NEXT_PUBLIC_FIREBASE_PROJECT_ID:          ${{ secrets.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
      NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET:      ${{ secrets.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
      NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
      NEXT_PUBLIC_FIREBASE_APP_ID:              ${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}
      NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID:      ${{ secrets.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
      - run: npm ci
      - run: npm run build
  test:
    environment: sparksmart-env
    env: 
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:        ${{ secrets.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY }}
      CLERK_SECRET_KEY:                         ${{ secrets.CLERK_SECRET_KEY }}
      NEXT_PUBLIC_FIREBASE_API_KEY:             ${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}
      NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN:         ${{ secrets.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
      NEXT_PUBLIC_FIREBASE_PROJECT_ID:          ${{ secrets.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
      NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET:      ${{ secrets.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
      NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
      NEXT_PUBLIC_FIREBASE_APP_ID:              ${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}
      NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID:      ${{ secrets.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
      - run: npm ci
      - run: npm run test